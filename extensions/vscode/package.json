{"name": "continue", "icon": "media/icon.png", "author": "Continue Dev, Inc", "version": "1.1.72", "repository": {"type": "git", "url": "https://github.com/continuedev/continue"}, "extensionKind": ["ui", "workspace"], "bugs": {"url": "https://github.com/continuedev/continue/issues", "email": "<EMAIL>"}, "homepage": "https://continue.dev", "qna": "https://github.com/continuedev/continue/issues/new/choose", "license": "Apache-2.0", "displayName": "Continue - open-source AI code assistant", "pricing": "Free", "description": "The leading open-source AI code assistant", "publisher": "Continue", "engines": {"vscode": "^1.70.0", "node": ">=20.19.0"}, "engine-strict": true, "galleryBanner": {"color": "#1E1E1E", "theme": "dark"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Machine Learning", "Snippets"], "keywords": ["chatgpt", "cline", "roo", "github", "copilot", "claude", "mistral", "codestral", "ai"], "activationEvents": ["onUri", "onStartupFinished", "onView:continueGUIView"], "main": "./out/extension.js", "contributes": {"languages": [{"filenames": ["config.json", ".aimi_rc.json"], "id": "jsonc"}, {"id": "promptLanguage", "extensions": [".prompt"], "aliases": ["Prompt Language"], "configuration": "./prompt-file-language-configuration.json"}], "grammars": [{"language": "promptLanguage", "scopeName": "source.prompt", "path": "./media/prompt.tmLanguage.json"}], "configuration": {"title": "Continue", "properties": {"continue.telemetryEnabled": {"type": "boolean", "default": true, "markdownDescription": "Continue collects anonymous usage data, cleaned of PII, to help us improve the product for our users. Read more  at [continue.dev › Telemetry](https://docs.continue.dev/telemetry)."}, "continue.showInlineTip": {"type": "boolean", "default": true, "description": "Show inline suggestion to use the Continue keyboard shortcuts (e.g. \"Cmd/Ctrl L to select code, Cmd/Ctrl I to edit\")."}, "continue.disableQuickFix": {"type": "boolean", "default": false, "description": "Disable the quick fix feature."}, "continue.enableQuickActions": {"type": "boolean", "default": false, "markdownDescription": "Enable the experimental Quick Actions feature. Read our walkthrough to learn about configuration and how to share feedback: [continue.dev › Walkthrough: Quick Actions (experimental)](https://docs.continue.dev/customize/deep-dives/vscode-actions#quick-actions)"}, "continue.enableTabAutocomplete": {"type": "boolean", "default": true, "markdownDescription": "Enable Continue's tab autocomplete feature. Read our walkthrough to learn about configuration and how to share feedback: [continue.dev › Walkthrough: Tab Autocomplete (beta)](https://docs.continue.dev/features/tab-autocomplete)"}, "continue.pauseTabAutocompleteOnBattery": {"type": "boolean", "default": false, "markdownDescription": "Pause Continue's tab autocomplete feature when your battery is low."}, "continue.pauseCodebaseIndexOnStart": {"type": "boolean", "default": false, "markdownDescription": "Pause Continue's codebase index on start."}, "continue.enableConsole": {"type": "boolean", "default": false, "markdownDescription": "Enable a console to log and explore model inputs and outputs. It can be found in the bottom panel."}, "continue.remoteConfigServerUrl": {"type": "string", "default": null, "markdownDescription": "If your team is set up to use shared configuration, enter the server URL here and your user token below to enable automatic syncing."}, "continue.userToken": {"type": "string", "default": null, "markdownDescription": "If your team is set up to use shared configuration, enter your user token here and your server URL above to enable automatic syncing."}, "continue.remoteConfigSyncPeriod": {"type": "number", "default": 60, "description": "The period of time in minutes between automatic syncs."}}}, "commands": [{"command": "continue.applyCodeFromChat", "category": "Continue", "title": "Apply code from chat", "group": "Continue"}, {"command": "continue.acceptDiff", "category": "Continue", "title": "Accept <PERSON><PERSON>", "group": "Continue"}, {"command": "continue.rejectDiff", "category": "Continue", "title": "Reject Diff", "group": "Continue", "icon": "$(stop)"}, {"command": "continue.acceptVerticalDiffBlock", "category": "Continue", "title": "Accept Vertical Diff Block", "group": "Continue"}, {"command": "continue.rejectVerticalDiffBlock", "category": "Continue", "title": "Reject Vertical Diff Block", "group": "Continue"}, {"command": "continue.focusEdit", "category": "Continue", "title": "Edit Highlighted Code", "group": "Continue"}, {"command": "continue.focusContinueInput", "category": "Continue", "title": "Add Highlighted Code to Context and Clear Chat", "group": "Continue"}, {"command": "continue.focusContinueInputWithoutClear", "category": "Continue", "title": "Add Highlighted Code to Context", "group": "Continue"}, {"command": "continue.debugTerminal", "category": "Continue", "title": "Debug Terminal", "group": "Continue"}, {"command": "continue.exitEditMode", "category": "Continue", "title": "Exit Edit Mode", "group": "Continue"}, {"command": "continue.toggleFullScreen", "category": "Continue", "title": "Open in new window", "icon": "$(link-external)", "group": "Continue"}, {"command": "continue.openConfigPage", "category": "Continue", "title": "Open Settings", "icon": "$(gear)", "group": "Continue"}, {"command": "continue.toggleTabAutocompleteEnabled", "category": "Continue", "title": "Toggle Autocomplete Enabled", "group": "Continue"}, {"command": "continue.forceAutocomplete", "title": "Continue: Force Autocomplete", "category": "Continue"}, {"command": "continue.selectFilesAsContext", "category": "Continue", "title": "Select Files as Context", "group": "Continue"}, {"command": "continue.newSession", "category": "Continue", "title": "New Session", "icon": "$(add)", "group": "Continue"}, {"command": "continue.viewHistory", "category": "Continue", "title": "View History", "icon": "$(history)", "group": "Continue"}, {"command": "continue.viewLogs", "category": "Continue", "title": "View History", "group": "Continue"}, {"command": "continue.clearConsole", "category": "Continue", "title": "Clear Console", "icon": "$(clear-all)", "group": "Continue"}, {"command": "continue.navigateTo", "category": "Continue", "title": "Navigate to a path", "group": "Continue"}, {"command": "continue.writeCommentsForCode", "category": "Continue", "title": "Write Comments for this Code", "group": "Continue"}, {"command": "continue.writeDocstringForCode", "category": "Continue", "title": "Write a Docstring for this Code", "group": "Continue"}, {"command": "continue.fixCode", "category": "Continue", "title": "Fix this Code", "group": "Continue"}, {"command": "continue.optimizeCode", "category": "Continue", "title": "Optimize this Code", "group": "Continue"}, {"command": "continue.fixGrammar", "category": "Continue", "title": "Fix Grammar / Spelling", "group": "Continue"}, {"command": "continue.codebaseForceReIndex", "category": "Continue", "title": "Codebase Force Re-Index", "group": "Continue"}, {"command": "continue.rebuildCodebaseIndex", "category": "Continue", "title": "Rebuild codebase index", "group": "Continue"}, {"command": "continue.docsIndex", "category": "Continue", "title": "Docs Index", "group": "Continue"}, {"command": "continue.docsReIndex", "category": "Continue", "title": "Docs Force Re-Index", "group": "Continue"}, {"command": "continue.focusContinueInput", "category": "Continue", "title": "Focus Continue Chat", "group": "Continue"}, {"command": "continue.enterEnterpriseLicenseKey", "category": "Continue", "title": "Enter Enterprise License Key", "group": "Continue"}, {"command": "continue.nextEditWindow.hideNextEditSuggestion", "category": "Continue", "title": "Hide Next Edit Suggestion"}, {"command": "continue.nextEditWindow.acceptNextEditSuggestion", "category": "Continue", "title": "Accept Next Edit Suggestion"}, {"command": "continue.forceNextEdit", "category": "Continue", "title": "Continue: Force Next Edit"}, {"command": "continue.acceptJump", "category": "Continue", "title": "Continue: Accept Jump Suggestion"}, {"command": "continue.rejectJump", "category": "Continue", "title": "Continue: Reject Jump Suggestion"}, {"command": "continue.generateRule", "category": "Continue", "title": "Generate Rule", "group": "Continue"}], "keybindings": [{"command": "continue.focusContinueInput", "mac": "cmd+l", "key": "ctrl+l"}, {"command": "continue.focusContinueInputWithoutClear", "mac": "cmd+shift+l", "key": "ctrl+shift+l"}, {"command": "continue.acceptDiff", "mac": "shift+cmd+enter", "key": "shift+ctrl+enter", "when": "continue.diffVisible"}, {"command": "continue.rejectDiff", "mac": "shift+cmd+backspace", "key": "shift+ctrl+backspace", "when": "continue.diffVisible"}, {"command": "continue.rejectDiff", "mac": "cmd+z", "key": "ctrl+z", "when": "continue.diffVisible"}, {"command": "continue.quickEditHistoryUp", "mac": "up", "key": "up", "when": "false && continue.quickEditHistoryFocused"}, {"command": "continue.quickEditHistoryDown", "mac": "down", "key": "down", "when": "false && continue.quickEditHistoryFocused"}, {"command": "continue.acceptVerticalDiffBlock", "mac": "alt+cmd+y", "key": "alt+ctrl+y"}, {"command": "continue.rejectVerticalDiffBlock", "mac": "alt+cmd+n", "key": "alt+ctrl+n"}, {"command": "continue.focusEdit", "title": "Edit code with natural language", "mac": "cmd+i", "key": "ctrl+i"}, {"command": "continue.exitEditMode", "mac": "escape", "key": "escape", "when": "continue.inEditMode && editorFocus"}, {"command": "continue.debugTerminal", "mac": "cmd+shift+r", "key": "ctrl+shift+r"}, {"command": "continue.toggleFullScreen", "mac": "cmd+k cmd+m", "key": "ctrl+k ctrl+m", "when": "!terminalFocus"}, {"command": "continue.toggleTabAutocompleteEnabled", "mac": "cmd+k cmd+a", "key": "ctrl+k ctrl+a", "when": "!terminalFocus"}, {"command": "continue.forceAutocomplete", "key": "ctrl+alt+space", "mac": "cmd+alt+space", "when": "editorTextFocus && !editorHasSelection && !editorReadOnly && !inSnippetMode"}, {"command": "continue.applyCodeFromChat", "mac": "alt+a", "key": "alt+a"}, {"command": "continue.nextEditWindow.hideNextEditSuggestion", "key": "escape", "when": "editorTextFocus && nextEditWindowActive && !continue.jumpDecorationVisible"}, {"command": "continue.nextEditWindow.acceptNextEditSuggestion", "key": "tab", "when": "editorTextFocus && nextEditWindowActive && !continue.jumpDecorationVisible"}, {"command": "continue.acceptJump", "key": "tab", "when": "editorTextFocus && continue.jumpDecorationVisible"}, {"command": "continue.rejectJump", "key": "escape", "when": "editorTextFocus && continue.jumpDecorationVisible"}], "submenus": [{"id": "continue.continueSubMenu", "label": "Continue"}], "menus": {"commandPalette": [{"command": "continue.focusContinueInput"}, {"command": "continue.focusContinueInputWithoutClear"}, {"command": "continue.debugTerminal"}, {"command": "continue.toggleFullScreen"}, {"command": "continue.newSession"}, {"command": "continue.enterEnterpriseLicenseKey"}, {"command": "continue.generateRule"}], "editor/context": [{"submenu": "continue.continueSubMenu", "group": "0_acontinue"}], "editor/title/run": [{"command": "continue.rejectDiff", "group": "Continue", "when": "continue.streamingDiff"}], "continue.continueSubMenu": [{"command": "continue.focusContinueInputWithoutClear", "group": "Continue", "when": "editorHasSelection"}, {"command": "continue.focusEdit", "group": "Continue", "when": "editorHasSelection && !editorR<PERSON>only"}, {"command": "continue.writeCommentsForCode", "group": "Continue", "when": "editorHasSelection && !editorR<PERSON>only"}, {"command": "continue.writeDocstringForCode", "group": "Continue", "when": "editorHasSelection && !editorR<PERSON>only"}, {"command": "continue.fixCode", "group": "Continue", "when": "editorHasSelection && !editorR<PERSON>only"}, {"command": "continue.optimizeCode", "group": "Continue", "when": "editorHasSelection && !editorR<PERSON>only"}, {"command": "continue.fixGrammar", "group": "Continue", "when": "editorHasSelection && editorLangId == 'markdown' && !editorReadonly"}], "explorer/context": [{"command": "continue.selectFilesAsContext", "group": "1_debug@1"}], "view/title": [{"command": "continue.newSession", "group": "navigation@1", "when": "view == continue.continueGUIView"}, {"command": "continue.viewHistory", "group": "navigation@2", "when": "view == continue.continueGUIView"}, {"command": "continue.toggleFullScreen", "group": "navigation@3", "when": "view == continue.continueGUIView"}, {"command": "continue.openConfigPage", "group": "navigation@4", "when": "view == continue.continueGUIView"}, {"command": "continue.clearConsole", "group": "navigation@1", "when": "view == continue.continueConsoleView"}], "editor/title": [{"command": "continue.newSession", "group": "navigation@1", "when": "activeWebviewPanelId == continue.continueGUIView"}, {"command": "continue.viewHistory", "group": "navigation@2", "when": "activeWebviewPanelId == continue.continueGUIView"}], "terminal/context": [{"command": "continue.debugTerminal", "group": "navigation@top"}]}, "viewsContainers": {"activitybar": [{"id": "continue", "title": "Continue", "icon": "media/sidebar-icon.png"}], "panel": [{"id": "continueConsole", "title": "Con<PERSON><PERSON> Console", "icon": "$(window)"}]}, "views": {"continue": [{"type": "webview", "id": "continue.continueGUIView", "name": "Continue", "icon": "media/sidebar-icon.png", "visibility": "visible"}], "continueConsole": [{"type": "webview", "id": "continue.continueConsoleView", "name": "Con<PERSON><PERSON> Console", "icon": "$(window)", "visibility": "visible", "when": "config.continue.enableConsole"}]}, "jsonValidation": [{"fileMatch": "**/.continue*/config.json", "url": "./config_schema.json"}, {"fileMatch": ".aimi_rc.json", "url": "./aimi_rc_schema.json"}, {"fileMatch": "**/config.yaml", "url": "./config-yaml-schema.json"}]}, "scripts": {"esbuild-base": "node scripts/esbuild.js", "vscode:prepublish": "npm run esbuild-base -- --minify", "esbuild": "npm run esbuild-base -- --sourcemap", "esbuild-watch": "npm run esbuild-base -- --sourcemap --watch", "esbuild-notify": "npm run esbuild-base -- --sourcemap --notify", "esbuild:visualize": "esbuild-visualizer --metadata ./build/meta.json --filename ./build/stats.html --open", "tsc": "tsc -p ./", "tsc:check": "tsc -p ./ --noEmit", "tsc-watch": "tsc -watch -p ./", "rebuild": "electron-rebuild -v 19.1.8 node-pty", "lint": "eslint src --ext ts", "lint:fix": "eslint . --ext ts --fix", "test": "vitest run", "vitest": "vitest run", "write-build-timestamp": "node scripts/write-build-timestamp.js", "prepackage": "node scripts/prepackage.js", "package": "node scripts/package.js", "package-all": "node scripts/package-all.js", "package:pre-release": "node scripts/package.js --pre-release", "build:rust": "cargo-cp-artifact -ac sync sync.node -- cargo build --manifest-path ../../sync/Cargo.toml --message-format=json-render-diagnostics", "build-debug:rust": "npm run build:rust --", "build-release:rust": "npm run build:rust -- --release", "e2e:compile": "tsc -p ./tsconfig.e2e.json", "e2e:build": "npm --prefix ../../gui run build && npm run package", "e2e:create-storage": "mkdir -p ./e2e/storage", "e2e:get-chromedriver": "extest get-chromedriver --storage ./e2e/storage --code_version 1.95.0", "e2e:get-vscode": "extest get-vscode --storage ./e2e/storage --code_version 1.95.0", "e2e:sign-vscode": "codesign --entitlements entitlements.plist --deep --force -s - './e2e/storage/Visual Studio Code.app'", "e2e:copy-vsix": "chmod +x ./e2e/get-latest-vsix.sh && bash ./e2e/get-latest-vsix.sh", "e2e:install-vsix": "extest install-vsix -f ./e2e/vsix/continue.vsix --extensions_dir ./e2e/.test-extensions --storage ./e2e/storage", "e2e:install-extensions": "extest install-from-marketplace ms-vscode-remote.remote-ssh --extensions_dir ./e2e/.test-extensions --storage ./e2e/storage && extest install-from-marketplace ms-vscode-remote.remote-containers --extensions_dir ./e2e/.test-extensions --storage ./e2e/storage && extest install-from-marketplace ms-vscode-remote.remote-wsl --extensions_dir ./e2e/.test-extensions --storage ./e2e/storage", "e2e:test": "extest run-tests ${TEST_FILE:-'./e2e/_output/tests/*.test.js'} --code_settings settings.json --extensions_dir ./e2e/.test-extensions --storage ./e2e/storage", "e2e:clean": "rm -rf ./e2e/_output ./e2e/storage", "e2e:all": "npm run e2e:build && npm run e2e:compile && npm run e2e:create-storage && npm run e2e:get-chromedriver && npm run e2e:get-vscode && npm run e2e:sign-vscode && npm run e2e:copy-vsix && npm run e2e:install-vsix && npm run e2e:install-extensions && CONTINUE_GLOBAL_DIR=e2e/test-continue npm run e2e:test && npm run e2e:clean", "e2e:all-non-mac": "npm run e2e:build && npm run e2e:compile && npm run e2e:create-storage && npm run e2e:get-chromedriver && npm run e2e:get-vscode && npm run e2e:copy-vsix && npm run e2e:install-vsix && npm run e2e:install-extensions && CONTINUE_GLOBAL_DIR=e2e/test-continue npm run e2e:test && npm run e2e:clean", "e2e:recompile-extension": "npm run package && npm run e2e:compile && npm run e2e:copy-vsix && npm run e2e:install-vsix && npm run e2e:install-extensions && CONTINUE_GLOBAL_DIR=e2e/test-continue npm run e2e:test && npm run e2e:clean", "e2e:rebuild-gui": "rm -rf gui && cp -r ../../gui/dist gui && npm run package && npm run e2e:copy-vsix && npm run e2e:install-vsix && npm run e2e:install-extensions && CONTINUE_GLOBAL_DIR=e2e/test-continue npm run e2e:test && npm run e2e:clean", "e2e:quick": "npm run e2e:compile && CONTINUE_GLOBAL_DIR=e2e/test-continue npm run e2e:test && npm run e2e:clean", "e2e:ci:download": "npm run e2e:create-storage && npm run e2e:get-chromedriver && npm run e2e:get-vscode", "e2e:ci:run": "npm run e2e:compile && npm run e2e:copy-vsix && npm run e2e:install-vsix && npm run e2e:install-extensions && CONTINUE_GLOBAL_DIR=e2e/test-continue npm run e2e:test", "e2e:ci:run-yaml": "npm run e2e:compile && npm run e2e:copy-vsix && npm run e2e:install-vsix && npm run e2e:install-extensions && CONTINUE_GLOBAL_DIR=e2e/test-continue-yaml npm run e2e:test"}, "devDependencies": {"@biomejs/biome": "1.6.4", "@nestjs/common": "^11.0.16", "@openapitools/openapi-generator-cli": "^2.20.0", "@types/chai": "^5.0.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/follow-redirects": "^1.14.4", "@types/mocha": "^10.0.10", "@types/node": "^20.0.0", "@types/node-fetch": "^2.6.12", "@types/react-dom": "^18.2.4", "@types/request": "^2.48.8", "@types/uuid": "^10.0.0", "@types/vscode": "1.70", "@types/ws": "^8.5.4", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vscode/vsce": "^2.22.0", "cargo-cp-artifact": "^0.1", "chai": "^4.5.0", "esbuild": "0.17.19", "esbuild-visualizer": "^0.6.0", "eslint": "^8.28.0", "json-schema-to-typescript": "^12.0.0", "ovsx": "^0.8.3", "rimraf": "^5.0.5", "typescript": "^5.6.3", "vite": "^4.5.14", "vscode-extension-tester": "^8.14.1"}, "dependencies": {"@continuedev/config-types": "file:../../packages/config-types", "@continuedev/fetch": "file:../../packages/fetch", "@electron/rebuild": "^3.2.10", "@reduxjs/toolkit": "^1.9.3", "@vscode/ripgrep": "^1.15.9", "axios": "^1.2.5", "core": "file:../../core", "cors": "^2.8.5", "dbinfoz": "^0.14.0", "diff": "^7.0.0", "downshift": "^7.6.0", "esbuild": "0.17.19", "express": "^4.18.2", "fkill": "^8.1.0", "follow-redirects": "^1.15.4", "handlebars": "^4.7.8", "highlight.js": "^11.7.0", "highlightable": "^1.3.0-beta.0", "http-proxy": "^1.18.1", "http-proxy-agent": "^7.0.0", "http-proxy-middleware": "^2.0.9", "https-proxy-agent": "^7.0.2", "ignore": "^5.3.0", "jsdom": "^24.0.0", "lru-cache": "^11.0.2", "minisearch": "^7.0.0", "mocha": "^11.7.1", "monaco-editor": "^0.45.0", "monaco-vscode-textmate-theme-converter": "^0.1.7", "ncp": "^2.0.0", "node-fetch": "^3.3.2", "node-machine-id": "^1.1.12", "posthog-node": "^3.6.3", "react-markdown": "^8.0.7", "react-redux": "^8.0.5", "read-last-lines": "^1.8.0", "request": "^2.88.2", "socket.io-client": "^4.7.2", "strip-ansi": "^7.1.0", "svg-builder": "^2.0.0", "systeminformation": "^5.23.7", "tailwindcss": "^3.3.2", "undici": "^6.21.3", "uuid": "^9.0.1", "uuidv4": "^6.2.13", "vectordb": "^0.4.20", "vitest": "^3.1.4", "vscode-languageclient": "^8.0.2", "ws": "^8.13.0", "yarn": "^1.22.21"}, "overrides": {"vitest": {"@types/node": "^20.0.0"}}}